// TrueBDC CRM Automation Suite - Background Service Worker

class TrueBDCBackground {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeDefaultSettings();
    }

    setupEventListeners() {
        // Extension installation/update
        chrome.runtime.onInstalled.addListener((details) => {
            this.handleInstallation(details);
        });

        // Message handling from content scripts and popup
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // Keep message channel open for async responses
        });

        // Tab updates
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            this.handleTabUpdate(tabId, changeInfo, tab);
        });

        // Storage changes
        chrome.storage.onChanged.addListener((changes, namespace) => {
            this.handleStorageChange(changes, namespace);
        });
    }

    async handleInstallation(details) {
        console.log('TrueBDC Extension installed/updated:', details);

        if (details.reason === 'install') {
            // First time installation
            await this.initializeDefaultSettings();
            
            // Show welcome notification
            chrome.notifications.create({
                type: 'basic',
                iconUrl: 'icons/icon48.png',
                title: 'TrueBDC CRM Automation',
                message: 'Extension installed successfully! Click the extension icon to get started.'
            });
        } else if (details.reason === 'update') {
            // Extension updated
            console.log('Extension updated to version:', chrome.runtime.getManifest().version);
        }
    }

    async initializeDefaultSettings() {
        try {
            const result = await chrome.storage.local.get('settings');
            
            if (!result.settings) {
                const defaultSettings = {
                    dealershipName: 'Downtown Auto Center',
                    agentName: '',
                    airtableApiKey: '',
                    airtableBaseId: 'appexR9tFKGHjSWNE',
                    refreshInterval: 5,
                    enableNotifications: true,
                    debugMode: false
                };

                await chrome.storage.local.set({ 
                    settings: defaultSettings,
                    profiles: [],
                    version: chrome.runtime.getManifest().version
                });

                console.log('Default settings initialized');
            }

            // Initialize default script states
            const scripts = [
                'dynamicTabTitle',
                'bypassRefresh', 
                'fasterRooftops',
                'clickToCall',
                'tabToPopup',
                'autoRefresh',
                'callingText'
            ];

            for (const script of scripts) {
                const scriptResult = await chrome.storage.local.get(`script_${script}`);
                if (scriptResult[`script_${script}`] === undefined) {
                    await chrome.storage.local.set({ [`script_${script}`]: false });
                }
            }

        } catch (error) {
            console.error('Error initializing default settings:', error);
        }
    }

    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'getSettings':
                    const settings = await this.getSettings();
                    sendResponse({ success: true, data: settings });
                    break;

                case 'updateSettings':
                    await this.updateSettings(message.settings);
                    sendResponse({ success: true });
                    break;

                case 'getScriptState':
                    const state = await this.getScriptState(message.script);
                    sendResponse({ success: true, enabled: state });
                    break;

                case 'toggleScript':
                    await this.toggleScript(message.script, message.enabled);
                    sendResponse({ success: true });
                    break;

                case 'showNotification':
                    await this.showNotification(message.title, message.message, message.type);
                    sendResponse({ success: true });
                    break;

                case 'logActivity':
                    await this.logActivity(message.activity, message.data);
                    sendResponse({ success: true });
                    break;

                case 'checkCRMCompatibility':
                    const compatibility = await this.checkCRMCompatibility(sender.tab.url);
                    sendResponse({ success: true, compatible: compatibility });
                    break;

                default:
                    console.warn('Unknown message action:', message.action);
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Error handling message:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async handleTabUpdate(tabId, changeInfo, tab) {
        // Only process when page is completely loaded
        if (changeInfo.status !== 'complete' || !tab.url) return;

        // Check if this is a CRM page
        const isCRMPage = this.isCRMPage(tab.url);
        
        if (isCRMPage) {
            // Inject content scripts if needed
            try {
                await chrome.scripting.executeScript({
                    target: { tabId: tabId },
                    files: ['content.js']
                });
            } catch (error) {
                // Content script might already be injected
                console.log('Content script injection skipped:', error.message);
            }

            // Update extension badge
            chrome.action.setBadgeText({
                tabId: tabId,
                text: 'ON'
            });
            chrome.action.setBadgeBackgroundColor({
                tabId: tabId,
                color: '#28a745'
            });
        } else {
            // Clear badge for non-CRM pages
            chrome.action.setBadgeText({
                tabId: tabId,
                text: ''
            });
        }
    }

    handleStorageChange(changes, namespace) {
        if (namespace === 'local') {
            // Notify all CRM tabs about storage changes
            this.notifyAllCRMTabs('storageChanged', changes);
        }
    }

    async getSettings() {
        const result = await chrome.storage.local.get('settings');
        return result.settings || {};
    }

    async updateSettings(newSettings) {
        await chrome.storage.local.set({ settings: newSettings });
        
        // Notify all CRM tabs about settings update
        this.notifyAllCRMTabs('settingsUpdated', newSettings);
    }

    async getScriptState(scriptName) {
        const result = await chrome.storage.local.get(`script_${scriptName}`);
        return result[`script_${scriptName}`] || false;
    }

    async toggleScript(scriptName, enabled) {
        await chrome.storage.local.set({ [`script_${scriptName}`]: enabled });
        
        // Notify all CRM tabs about script state change
        this.notifyAllCRMTabs('scriptToggled', { script: scriptName, enabled });
    }

    async showNotification(title, message, type = 'basic') {
        const settings = await this.getSettings();
        
        if (!settings.enableNotifications) return;

        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'icons/icon48.png',
            title: title,
            message: message
        });
    }

    async logActivity(activity, data) {
        const timestamp = new Date().toISOString();
        const logEntry = {
            timestamp,
            activity,
            data,
            url: data.url || 'unknown'
        };

        // Store activity log (keep last 100 entries)
        const result = await chrome.storage.local.get('activityLog');
        const activityLog = result.activityLog || [];
        
        activityLog.unshift(logEntry);
        if (activityLog.length > 100) {
            activityLog.splice(100);
        }

        await chrome.storage.local.set({ activityLog });
    }

    async checkCRMCompatibility(url) {
        const crmPatterns = [
            /eleadcrm\.com/i,
            /vinsolutions\.com/i,
            /dealersocket\.com/i,
            /cdk\.com/i
        ];

        return crmPatterns.some(pattern => pattern.test(url));
    }

    isCRMPage(url) {
        if (!url) return false;
        
        const crmDomains = [
            'eleadcrm.com',
            'vinsolutions.com',
            'dealersocket.com',
            'cdk.com'
        ];

        return crmDomains.some(domain => url.includes(domain));
    }

    async notifyAllCRMTabs(action, data) {
        try {
            const tabs = await chrome.tabs.query({});
            
            for (const tab of tabs) {
                if (this.isCRMPage(tab.url)) {
                    try {
                        await chrome.tabs.sendMessage(tab.id, {
                            action,
                            data
                        });
                    } catch (error) {
                        // Tab might not have content script loaded
                        console.log(`Could not notify tab ${tab.id}:`, error.message);
                    }
                }
            }
        } catch (error) {
            console.error('Error notifying CRM tabs:', error);
        }
    }
}

// Initialize background service
new TrueBDCBackground();

// TrueBDC CRM Automation Suite - Add Calling Text and Shortcut to Change Time

class CallingText {
    constructor(settings = {}) {
        this.settings = settings;
        this.isActive = false;
        this.keydownHandler = null;
        this.repName = settings.agentName || '';

        // Constants matching the original script
        this.TEXTAREA_ID = 'textComments';
        this.DATE_INPUT_ID = 'textDate';
        this.SAVE_BUTTON_ID = 'buttonSave';
        this.REP_NAME_KEY = 'repName';
        this.DEFAULT_REP_NAME = 'Rep';

        this.init();
    }

    init() {
        try {
            TrueBDCUtils.log('Initializing Add Calling Text and Time Shortcut');

            // Check if we're on a supported page
            if (this.isSupportedPage()) {
                this.setupKeyListener();
                this.initializeScript();
                this.isActive = true;

                TrueBDCUtils.log('Add Calling Text and Time Shortcut activated');
                TrueBDCUtils.logActivity('calling_text_activated', {
                    url: window.location.href,
                    repName: this.repName
                });
            } else {
                TrueBDCUtils.log('Add Calling Text and Time Shortcut not activated - unsupported page');
            }
        } catch (error) {
            TrueBDCUtils.error('Failed to initialize Add Calling Text and Time Shortcut', error);
        }
    }

    isSupportedPage() {
        const url = window.location.href;
        // Exact URL pattern from the original script
        const supportedPattern = /eleadcrm\.com\/evo2\/fresh\/elead-v45\/elead_track\/eleadToday\/CompletePhoneContact\.aspx/i;

        return supportedPattern.test(url);
    }

    setupKeyListener() {
        this.keydownHandler = (event) => {
            // Ctrl+M or Cmd+M: Add 1 minute to DateTime
            if ((event.ctrlKey && event.key === 'm') || (event.metaKey && event.key === 'm')) {
                event.preventDefault();
                this.incrementDateTime();
                TrueBDCUtils.log('DateTime increment shortcut (Ctrl/Cmd+M) triggered');
                return false;
            }

            // Ctrl+K or Cmd+K: Add 1 minute to DateTime and click Complete button
            if ((event.ctrlKey && event.key === 'k') || (event.metaKey && event.key === 'k')) {
                event.preventDefault();
                this.incrementDateTime();
                this.clickCompleteButton();
                TrueBDCUtils.log('DateTime increment + Complete shortcut (Ctrl/Cmd+K) triggered');
                return false;
            }

            // Ctrl+Alt+9 or Cmd+Alt+9: Toggle rep name input
            if ((event.ctrlKey && event.altKey && event.key === '9') || (event.metaKey && event.altKey && event.key === '9')) {
                event.preventDefault();
                this.toggleRepNameInput();
                TrueBDCUtils.log('Rep name input toggle shortcut (Ctrl/Cmd+Alt+9) triggered');
                return false;
            }
        };

        // Add event listener
        document.addEventListener('keydown', this.keydownHandler, true);
    }

    initializeScript() {
        // Initialize the script like the original
        this.updateTextarea();
        this.toggleRepNameInput();
    }

    // Rep name management module
    getRepName() {
        return localStorage.getItem(this.REP_NAME_KEY) || this.DEFAULT_REP_NAME;
    }

    setRepName(name) {
        localStorage.setItem(this.REP_NAME_KEY, name);
        this.updateTextarea();
        this.toggleRepNameInput();
    }

    // Function to update the textarea with rep name and "Calling" text
    updateTextarea() {
        const textareaElement = document.getElementById(this.TEXTAREA_ID);
        if (textareaElement) {
            textareaElement.value = `${this.getRepName()} -- Calling`;
            textareaElement.focus();
        }
    }

    // Function to add 1 minute to the date/time input
    incrementDateTime() {
        const dateInputElement = document.getElementById(this.DATE_INPUT_ID);
        if (dateInputElement) {
            const currentTime = new Date(dateInputElement.value);
            currentTime.setMinutes(currentTime.getMinutes() + 1);
            const formattedTime = this.formatDateTime(currentTime);
            dateInputElement.value = formattedTime;

            TrueBDCUtils.log('DateTime incremented by 1 minute', {
                newValue: formattedTime
            });
        }
    }

    // Function to format the date/time exactly like the original
    formatDateTime(date) {
        const hours = date.getHours() % 12 || 12;
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const amOrPm = date.getHours() >= 12 ? 'pm' : 'am';
        const formattedTime = `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear()} ${hours}:${minutes} ${amOrPm}`;
        return formattedTime;
    }

    // Function to click the "Complete" button
    clickCompleteButton() {
        const saveButton = document.getElementById(this.SAVE_BUTTON_ID);
        if (saveButton) {
            saveButton.click();
            TrueBDCUtils.log('Complete button clicked');
        }
    }

    createRepNameInput() {
        const container = document.createElement('div');
        container.style.position = 'fixed';
        container.style.bottom = '10px';
        container.style.right = '10px';
        container.style.display = 'flex';
        container.style.alignItems = 'center';
        container.style.zIndex = '999999';
        container.style.background = 'white';
        container.style.padding = '8px';
        container.style.borderRadius = '4px';
        container.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';
        container.id = 'truebdc-rep-name-input';

        const title = document.createElement('span');
        title.textContent = 'Input Your Name';
        title.style.fontSize = '10px';
        title.style.marginRight = '5px';
        container.appendChild(title);

        const repNameInput = document.createElement('input');
        repNameInput.type = 'text';
        repNameInput.placeholder = 'Enter Rep Name';
        repNameInput.value = this.getRepName();
        repNameInput.style.marginRight = '5px';
        container.appendChild(repNameInput);

        const saveButton = document.createElement('button');
        saveButton.textContent = 'Save';
        saveButton.onclick = () => this.setRepName(repNameInput.value);
        container.appendChild(saveButton);

        document.body.appendChild(container);
    }

    toggleRepNameInput() {
        const currentRepName = this.getRepName();
        const inputContainer = document.getElementById('truebdc-rep-name-input');

        if (currentRepName === this.DEFAULT_REP_NAME) {
            if (!inputContainer) {
                this.createRepNameInput();
            }
        } else {
            if (inputContainer) {
                inputContainer.remove();
            }
        }
    }



    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };

        if (newSettings.agentName && newSettings.agentName.trim()) {
            this.repName = newSettings.agentName.trim();
            TrueBDCUtils.log('Rep name updated from settings', { repName: this.repName });
        }
    }

    onPageChange() {
        // Handle page changes in SPAs
        if (this.isSupportedPage()) {
            if (!this.isActive) {
                this.init();
            } else {
                // Re-initialize on page change
                this.initializeScript();
            }
        } else {
            if (this.isActive) {
                this.destroy();
            }
        }
    }

    destroy() {
        try {
            // Remove event listeners
            if (this.keydownHandler) {
                document.removeEventListener('keydown', this.keydownHandler, true);
                this.keydownHandler = null;
            }

            // Remove rep name input if present
            const inputContainer = document.getElementById('truebdc-rep-name-input');
            if (inputContainer) {
                inputContainer.remove();
            }

            this.isActive = false;

            TrueBDCUtils.log('Add Calling Text and Time Shortcut destroyed');
            TrueBDCUtils.logActivity('calling_text_destroyed');
        } catch (error) {
            TrueBDCUtils.error('Error destroying Add Calling Text and Time Shortcut', error);
        }
    }

    // Static method to check if script should be available
    static isAvailable() {
        const url = window.location.href;
        return /eleadcrm\.com\/evo2\/fresh\/elead-v45\/elead_track\/eleadToday\/CompletePhoneContact\.aspx/i.test(url);
    }

    // Get current status
    getStatus() {
        return {
            isActive: this.isActive,
            isSupported: this.isSupportedPage(),
            hasKeyListener: !!this.keydownHandler,
            repName: this.getRepName(),
            shortcuts: {
                incrementTime: 'Ctrl+M (Windows) / Cmd+M (Mac)',
                incrementAndComplete: 'Ctrl+K (Windows) / Cmd+K (Mac)',
                toggleRepInput: 'Ctrl+Alt+9 (Windows) / Cmd+Alt+9 (Mac)'
            }
        };
    }
}

// Make class globally available
window.CallingText = CallingText;

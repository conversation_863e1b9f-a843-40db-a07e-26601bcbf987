// TrueBDC CRM Automation Suite - Add Calling Text and Shortcut to Change Time

class CallingText {
    constructor(settings = {}) {
        this.settings = settings;
        this.isActive = false;
        this.keydownHandler = null;
        this.repName = settings.agentName || '';
        
        this.init();
    }

    init() {
        try {
            TrueBDCUtils.log('Initializing Add Calling Text and Time Shortcut');

            // Check if we're on a supported page
            if (this.isSupportedPage()) {
                this.setupKeyListener();
                this.isActive = true;
                
                TrueBDCUtils.log('Add Calling Text and Time Shortcut activated');
                TrueBDCUtils.logActivity('calling_text_activated', {
                    url: window.location.href,
                    repName: this.repName
                });
            } else {
                TrueBDCUtils.log('Add Calling Text and Time Shortcut not activated - unsupported page');
            }
        } catch (error) {
            TrueBDCUtils.error('Failed to initialize Add Calling Text and Time Shortcut', error);
        }
    }

    isSupportedPage() {
        const url = window.location.href;
        const supportedPatterns = [
            /eleadcrm\.com\/evo2\/fresh\/elead-v45\/elead_track/i,
            /vinsolutions\.com/i
        ];

        return supportedPatterns.some(pattern => pattern.test(url));
    }

    setupKeyListener() {
        this.keydownHandler = (event) => {
            // Check for Ctrl+Shift+C (Windows) or Cmd+Shift+C (Mac) for calling text
            const isCallingShortcut = (event.ctrlKey || event.metaKey) && event.shiftKey && event.code === 'KeyC';
            
            // Check for Ctrl+Shift+T (Windows) or Cmd+Shift+T (Mac) for time adjustment
            const isTimeShortcut = (event.ctrlKey || event.metaKey) && event.shiftKey && event.code === 'KeyT';

            if (isCallingShortcut) {
                TrueBDCUtils.log('Calling text shortcut detected');
                event.preventDefault();
                event.stopPropagation();
                this.addCallingText();
                return false;
            }

            if (isTimeShortcut) {
                TrueBDCUtils.log('Time adjustment shortcut detected');
                event.preventDefault();
                event.stopPropagation();
                this.adjustDateTime();
                return false;
            }
        };

        // Add event listener
        window.addEventListener('keydown', this.keydownHandler, true);
        document.addEventListener('keydown', this.keydownHandler, true);
    }

    async addCallingText() {
        try {
            // Find focused textarea or text input
            const activeElement = document.activeElement;
            
            if (!activeElement || (activeElement.tagName !== 'TEXTAREA' && activeElement.tagName !== 'INPUT')) {
                // Try to find a textarea or input field to focus
                const textareas = document.querySelectorAll('textarea');
                const textInputs = document.querySelectorAll('input[type="text"]');
                
                let targetElement = null;
                
                // Prefer textareas over inputs
                if (textareas.length > 0) {
                    targetElement = textareas[0];
                } else if (textInputs.length > 0) {
                    targetElement = textInputs[0];
                }
                
                if (targetElement) {
                    targetElement.focus();
                    await TrueBDCUtils.sleep(100); // Give time for focus
                } else {
                    this.showNotification('No text field found to add calling text', 'warning');
                    return;
                }
            }

            // Get current rep name or prompt for it
            const repName = await this.getRepName();
            if (!repName) {
                this.showNotification('Rep name is required for calling text', 'warning');
                return;
            }

            // Create calling text with current timestamp
            const now = new Date();
            const timeString = now.toLocaleString('en-US', {
                month: '2-digit',
                day: '2-digit',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });

            const callingText = `${timeString} - ${repName} - Calling`;

            // Insert the text
            const targetElement = document.activeElement;
            if (targetElement) {
                const currentValue = targetElement.value || '';
                const cursorPosition = targetElement.selectionStart || currentValue.length;
                
                // Add calling text at cursor position or end
                const newValue = currentValue.slice(0, cursorPosition) + 
                                (currentValue ? '\n' : '') + 
                                callingText + 
                                currentValue.slice(cursorPosition);
                
                targetElement.value = newValue;
                
                // Position cursor after inserted text
                const newCursorPosition = cursorPosition + (currentValue ? 1 : 0) + callingText.length;
                targetElement.setSelectionRange(newCursorPosition, newCursorPosition);
                
                // Trigger change event
                targetElement.dispatchEvent(new Event('input', { bubbles: true }));
                targetElement.dispatchEvent(new Event('change', { bubbles: true }));
                
                this.showNotification(`Calling text added: ${callingText}`, 'success');
                
                TrueBDCUtils.log('Calling text added', {
                    text: callingText,
                    repName: repName,
                    elementTag: targetElement.tagName
                });
                
                TrueBDCUtils.logActivity('calling_text_added', {
                    text: callingText,
                    repName: repName,
                    timestamp: now.toISOString()
                });
            }

        } catch (error) {
            TrueBDCUtils.error('Error adding calling text', error);
            this.showNotification('Error adding calling text', 'error');
        }
    }

    async adjustDateTime() {
        try {
            // Find datetime input fields
            const dateTimeInputs = document.querySelectorAll('input[type="datetime-local"], input[type="datetime"], input[type="time"]');
            
            if (dateTimeInputs.length === 0) {
                this.showNotification('No datetime fields found on this page', 'warning');
                return;
            }

            let adjustedCount = 0;
            
            dateTimeInputs.forEach(input => {
                if (input.value) {
                    try {
                        const currentDate = new Date(input.value);
                        if (!isNaN(currentDate.getTime())) {
                            // Add 1 minute
                            currentDate.setMinutes(currentDate.getMinutes() + 1);
                            
                            // Format based on input type
                            let newValue;
                            if (input.type === 'time') {
                                newValue = currentDate.toTimeString().slice(0, 5); // HH:MM
                            } else {
                                // datetime-local format: YYYY-MM-DDTHH:MM
                                newValue = currentDate.toISOString().slice(0, 16);
                            }
                            
                            input.value = newValue;
                            
                            // Trigger change event
                            input.dispatchEvent(new Event('input', { bubbles: true }));
                            input.dispatchEvent(new Event('change', { bubbles: true }));
                            
                            adjustedCount++;
                        }
                    } catch (error) {
                        TrueBDCUtils.error('Error adjusting individual datetime field', error);
                    }
                }
            });

            if (adjustedCount > 0) {
                this.showNotification(`Adjusted ${adjustedCount} datetime field(s) (+1 minute)`, 'success');
                
                TrueBDCUtils.log('DateTime fields adjusted', {
                    count: adjustedCount
                });
                
                TrueBDCUtils.logActivity('datetime_adjusted', {
                    count: adjustedCount,
                    adjustment: '+1 minute',
                    timestamp: new Date().toISOString()
                });
            } else {
                this.showNotification('No datetime fields with values found to adjust', 'warning');
            }

        } catch (error) {
            TrueBDCUtils.error('Error adjusting datetime', error);
            this.showNotification('Error adjusting datetime fields', 'error');
        }
    }

    async getRepName() {
        // First check if we have a saved rep name in settings
        if (this.repName && this.repName.trim()) {
            return this.repName.trim();
        }

        // Check if we have a saved rep name in storage
        try {
            const result = await chrome.storage.local.get('repName');
            if (result.repName && result.repName.trim()) {
                this.repName = result.repName.trim();
                return this.repName;
            }
        } catch (error) {
            TrueBDCUtils.error('Error getting rep name from storage', error);
        }

        // Prompt user for rep name
        const userRepName = prompt(
            'Add Calling Text\n\n' +
            'Please enter your name/initials for calling text:\n' +
            '(This will be saved and used for future calling text)',
            ''
        );

        if (userRepName && userRepName.trim()) {
            this.repName = userRepName.trim();
            
            // Save to storage
            try {
                await chrome.storage.local.set({ repName: this.repName });
                
                // Also update settings if possible
                if (this.settings) {
                    this.settings.agentName = this.repName;
                    await TrueBDCUtils.sendMessage('updateSettings', { settings: this.settings });
                }
            } catch (error) {
                TrueBDCUtils.error('Error saving rep name', error);
            }

            TrueBDCUtils.log('Rep name saved', { repName: this.repName });
            return this.repName;
        }

        return null;
    }

    showNotification(message, type = 'info') {
        const notification = TrueBDCUtils.createElement('div', {
            class: 'truebdc-calling-notification'
        }, {
            position: 'fixed',
            top: '20px',
            left: '50%',
            transform: 'translateX(-50%)',
            background: this.getNotificationColor(type),
            color: 'white',
            padding: '12px 20px',
            borderRadius: '8px',
            fontSize: '14px',
            fontWeight: '500',
            zIndex: '999999',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
            opacity: '0',
            transition: 'opacity 0.3s ease',
            maxWidth: '400px',
            textAlign: 'center'
        });

        notification.textContent = message;
        document.body.appendChild(notification);

        // Fade in
        setTimeout(() => {
            notification.style.opacity = '1';
        }, 10);

        // Fade out and remove
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    getNotificationColor(type) {
        switch (type) {
            case 'success':
                return 'linear-gradient(135deg, #28a745, #20c997)';
            case 'warning':
                return 'linear-gradient(135deg, #ffc107, #e0a800)';
            case 'error':
                return 'linear-gradient(135deg, #dc3545, #c82333)';
            default:
                return 'linear-gradient(135deg, #007bff, #0056b3)';
        }
    }

    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        
        if (newSettings.agentName && newSettings.agentName.trim()) {
            this.repName = newSettings.agentName.trim();
            TrueBDCUtils.log('Rep name updated from settings', { repName: this.repName });
        }
    }

    onPageChange() {
        // Handle page changes in SPAs
        if (this.isSupportedPage()) {
            if (!this.isActive) {
                this.init();
            }
        } else {
            if (this.isActive) {
                this.destroy();
            }
        }
    }

    destroy() {
        try {
            // Remove event listeners
            if (this.keydownHandler) {
                window.removeEventListener('keydown', this.keydownHandler, true);
                document.removeEventListener('keydown', this.keydownHandler, true);
                this.keydownHandler = null;
            }

            this.isActive = false;
            
            TrueBDCUtils.log('Add Calling Text and Time Shortcut destroyed');
            TrueBDCUtils.logActivity('calling_text_destroyed');
        } catch (error) {
            TrueBDCUtils.error('Error destroying Add Calling Text and Time Shortcut', error);
        }
    }

    // Static method to check if script should be available
    static isAvailable() {
        const url = window.location.href;
        return /eleadcrm\.com|vinsolutions\.com/i.test(url);
    }

    // Get current status
    getStatus() {
        return {
            isActive: this.isActive,
            isSupported: this.isSupportedPage(),
            hasKeyListener: !!this.keydownHandler,
            repName: this.repName,
            shortcuts: {
                callingText: 'Ctrl+Shift+C (Windows) / Cmd+Shift+C (Mac)',
                timeAdjustment: 'Ctrl+Shift+T (Windows) / Cmd+Shift+T (Mac)'
            }
        };
    }
}

// Make class globally available
window.CallingText = CallingText;

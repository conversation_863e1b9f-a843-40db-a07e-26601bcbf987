// TrueBDC CRM Automation Suite - Simple Auto Refresh with Timer and Modal
// EXACT COPY of working Tampermonkey script adapted for Chrome extension

class AutoRefresh {
    constructor(settings = {}) {
        this.settings = settings;
        this.isActive = false;

        this.init();
    }

    init() {
        console.log('[TrueBDC AutoRefresh] Initializing...');
        console.log('[TrueBDC AutoRefresh] Current URL:', window.location.href);
        console.log('[TrueBDC AutoRefresh] Is in iframe:', window !== window.top);

        // Check if we're on a supported page (could be main page or iframe)
        if (this.isSupportedPage()) {
            console.log('[TrueBDC AutoRefresh] Supported page detected, starting script');
            this.startScript();
            this.isActive = true;
        } else {
            console.log('[TrueBDC AutoRefresh] Not a supported page - checking for iframes');
            console.log('[TrueBDC AutoRefresh] Current page:', window.location.pathname);
            console.log('[TrueBDC AutoRefresh] Target page should contain: /elead_track/weblink/weblinkToday.aspx');

            // Check if we're on the main CRM page and need to find the iframe
            this.checkForTargetIframe();

            // Set up navigation listener to detect when user navigates to the correct page
            this.setupNavigationListener();
        }
    }

    checkForTargetIframe() {
        console.log('[TrueBDC AutoRefresh] Checking for target iframe...');

        const urlPattern = this.getUrlPattern();
        console.log('[TrueBDC AutoRefresh] Looking for iframes matching pattern:', urlPattern);

        // Function to check iframes
        const checkIframes = () => {
            const iframes = document.querySelectorAll('iframe');
            console.log('[TrueBDC AutoRefresh] Found', iframes.length, 'iframes');

            iframes.forEach((iframe, index) => {
                try {
                    console.log(`[TrueBDC AutoRefresh] Checking iframe ${index}:`, iframe.src);

                    // Check if iframe src matches our target URL pattern
                    if (iframe.src && this.matchesUrlPattern(iframe.src, urlPattern)) {
                        console.log('[TrueBDC AutoRefresh] Found target iframe!', iframe.src);

                        // Wait for iframe to load and inject script
                        iframe.addEventListener('load', () => {
                            this.injectIntoIframe(iframe);
                        });

                        // If already loaded, inject immediately
                        if (iframe.contentDocument && iframe.contentDocument.readyState === 'complete') {
                            this.injectIntoIframe(iframe);
                        }
                    } else {
                        console.log(`[TrueBDC AutoRefresh] Iframe ${index} does not match pattern:`, iframe.src);
                    }
                } catch (error) {
                    console.log(`[TrueBDC AutoRefresh] Cannot access iframe ${index} (cross-origin):`, error.message);
                }
            });
        };

        // Check immediately
        checkIframes();

        // Also check periodically for dynamically created iframes
        setInterval(checkIframes, 2000);

        // Listen for new iframes being added
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.tagName === 'IFRAME') {
                        console.log('[TrueBDC AutoRefresh] New iframe detected:', node.src);
                        setTimeout(checkIframes, 500);
                    }
                });
            });
        });

        observer.observe(document.body, { childList: true, subtree: true });
    }

    injectIntoIframe(iframe) {
        try {
            console.log('[TrueBDC AutoRefresh] Injecting script into iframe...');
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            const iframeWindow = iframe.contentWindow;

            const urlPattern = this.getUrlPattern();

            // Check if the iframe contains our target page
            if (this.matchesUrlPattern(iframeWindow.location.href, urlPattern)) {
                console.log('[TrueBDC AutoRefresh] Iframe contains target page, starting script in iframe');
                console.log('[TrueBDC AutoRefresh] Iframe URL:', iframeWindow.location.href);

                // Execute the original script in the iframe context
                this.executeScriptInContext(iframeWindow, iframeDoc);
                this.isActive = true;
            } else {
                console.log('[TrueBDC AutoRefresh] Iframe URL does not match pattern:', iframeWindow.location.href);
            }
        } catch (error) {
            console.error('[TrueBDC AutoRefresh] Error injecting into iframe:', error);
        }
    }

    executeScriptInContext(targetWindow, targetDocument) {
        console.log('[TrueBDC AutoRefresh] Executing script in target context...');

        // Execute the original working script in the iframe context
        const script = targetDocument.createElement('script');
        script.textContent = `
            (function() {
                'use strict';
                console.log('[TrueBDC AutoRefresh] Script executing in iframe context');

                /** @type {number} */
                let intervalId = null;
                /** @type {boolean} */
                let isPaused = false;
                /** @type {number} */
                let countdown = 0;
                /** @type {number} */
                let refreshTime = Number(localStorage.getItem('autoRefreshTime')) || 5;

                console.log('[TrueBDC AutoRefresh] Script variables initialized in iframe:', { intervalId, isPaused, countdown, refreshTime });

                // Rest of the original script will be added here...
                ${this.getOriginalScriptContent()}
            })();
        `;

        targetDocument.head.appendChild(script);
        console.log('[TrueBDC AutoRefresh] Script injected into iframe');
    }

    setupNavigationListener() {
        console.log('[TrueBDC AutoRefresh] Setting up navigation listener...');

        // Listen for URL changes (for SPAs)
        let currentUrl = window.location.href;
        const checkForNavigation = () => {
            if (window.location.href !== currentUrl) {
                currentUrl = window.location.href;
                console.log('[TrueBDC AutoRefresh] Navigation detected to:', currentUrl);

                if (this.isSupportedPage() && !this.isActive) {
                    console.log('[TrueBDC AutoRefresh] Now on supported page, starting script');
                    this.startScript();
                    this.isActive = true;
                } else {
                    // Check for new iframes after navigation
                    setTimeout(() => this.checkForTargetIframe(), 1000);
                }
            }
        };

        // Check every second for URL changes
        setInterval(checkForNavigation, 1000);

        // Also listen for popstate events
        window.addEventListener('popstate', () => {
            setTimeout(() => {
                console.log('[TrueBDC AutoRefresh] Popstate event, checking URL:', window.location.href);
                checkForNavigation();
            }, 100);
        });
    }

    isSupportedPage() {
        const url = window.location.href;
        console.log('[TrueBDC AutoRefresh] Full URL:', url);
        console.log('[TrueBDC AutoRefresh] Hostname:', window.location.hostname);
        console.log('[TrueBDC AutoRefresh] Pathname:', window.location.pathname);

        // Get the URL pattern from settings or use default
        const urlPattern = this.getUrlPattern();
        console.log('[TrueBDC AutoRefresh] Using URL pattern:', urlPattern);

        const isSupported = this.matchesUrlPattern(url, urlPattern);
        console.log('[TrueBDC AutoRefresh] URL check - Supported:', isSupported);
        return isSupported;
    }

    getUrlPattern() {
        // Get pattern from settings, fallback to default
        const customPattern = this.settings?.iframeUrlPattern;
        if (customPattern && customPattern.trim()) {
            console.log('[TrueBDC AutoRefresh] Using custom URL pattern:', customPattern);
            // If user entered a full URL, extract just the path part
            if (customPattern.startsWith('http')) {
                try {
                    const url = new URL(customPattern);
                    const pathPattern = url.pathname + '*'; // Add wildcard to handle query params
                    console.log('[TrueBDC AutoRefresh] Converted full URL to path pattern:', pathPattern);
                    return pathPattern;
                } catch (e) {
                    console.log('[TrueBDC AutoRefresh] Invalid URL, using as-is:', customPattern);
                    return customPattern.trim();
                }
            }
            return customPattern.trim();
        }

        // Default pattern
        const defaultPattern = '/elead_track/weblink/weblinkToday.aspx*';
        console.log('[TrueBDC AutoRefresh] Using default URL pattern:', defaultPattern);
        return defaultPattern;
    }

    matchesUrlPattern(url, pattern) {
        console.log('[TrueBDC AutoRefresh] Matching URL:', url, 'against pattern:', pattern);

        // If pattern is a full URL, extract the path for comparison
        let comparePattern = pattern;
        if (pattern.startsWith('http')) {
            try {
                const patternUrl = new URL(pattern);
                comparePattern = patternUrl.pathname + '*'; // Add wildcard for query params
                console.log('[TrueBDC AutoRefresh] Converted pattern URL to path:', comparePattern);
            } catch (e) {
                console.log('[TrueBDC AutoRefresh] Invalid pattern URL, using as-is');
            }
        }

        // Handle wildcard patterns
        if (comparePattern.endsWith('*')) {
            const basePattern = comparePattern.slice(0, -1); // Remove the *
            const matches = url.includes(basePattern);
            console.log('[TrueBDC AutoRefresh] Wildcard match for:', basePattern, 'Result:', matches);
            return matches;
        } else {
            // Exact match
            const matches = url.includes(comparePattern);
            console.log('[TrueBDC AutoRefresh] Exact match for:', comparePattern, 'Result:', matches);
            return matches;
        }
    }

    getOriginalScriptContent() {
        return `
            /**
             * Create or update the timer display in the corner.
             */
            function createTimerDisplay() {
                console.log('[TrueBDC AutoRefresh] Creating timer display in iframe...');
                let timer = document.getElementById('autoRefreshTimerDisplay');
                if (!timer) {
                    console.log('[TrueBDC AutoRefresh] Timer element not found, creating new one in iframe');
                    timer = document.createElement('div');
                    timer.id = 'autoRefreshTimerDisplay';
                    timer.style.position = 'fixed';
                    timer.style.bottom = '20px';
                    timer.style.left = '20px';
                    timer.style.background = '#fff';
                    timer.style.color = '#222';
                    timer.style.padding = '10px 18px';
                    timer.style.borderRadius = '8px';
                    timer.style.boxShadow = '0 2px 8px rgba(0,0,0,0.15)';
                    timer.style.fontFamily = 'sans-serif';
                    timer.style.fontSize = '16px';
                    timer.style.cursor = 'pointer';
                    timer.style.zIndex = 10000;
                    timer.title = 'Click to set refresh interval';
                    timer.addEventListener('click', showModal);
                    document.body.appendChild(timer);
                    console.log('[TrueBDC AutoRefresh] Timer element created and added to iframe DOM');
                } else {
                    console.log('[TrueBDC AutoRefresh] Timer element found in iframe, updating content');
                }
                timer.textContent = isPaused ? \`⏸️ Paused\` : \`🔄 Refresh in \${countdown}s\`;
                timer.style.opacity = isPaused ? '0.5' : '1';
                console.log('[TrueBDC AutoRefresh] Timer display updated in iframe:', timer.textContent);
            }

            /**
             * Show a modal to set the refresh interval.
             */
            function showModal() {
                console.log('[TrueBDC AutoRefresh] showModal called in iframe');
                if (document.getElementById('refreshTimeModal')) {
                    console.log('[TrueBDC AutoRefresh] Modal already exists in iframe, returning');
                    return;
                }
                isPaused = true;
                updateDisplay();

                const timer = document.getElementById('autoRefreshTimerDisplay');
                const timerRect = timer.getBoundingClientRect();
                console.log('[TrueBDC AutoRefresh] Timer position in iframe:', timerRect);

                const modal = document.createElement('div');
                modal.id = 'refreshTimeModal';
                modal.style.position = 'fixed';
                modal.style.left = timerRect.left + 'px';
                modal.style.bottom = (window.innerHeight - timerRect.top) + 'px';
                modal.style.background = '#fff';
                modal.style.padding = '5px 7.5px'; // Modified: 50% reduced padding
                modal.style.borderRadius = '10px';
                modal.style.boxShadow = '0 4px 16px rgba(0,0,0,0.2)';
                modal.style.zIndex = 10001;
                modal.style.fontFamily = 'sans-serif';
                modal.style.fontSize = '12px'; // Modified: reduced font size

                modal.innerHTML = \`
                    <label for="refreshTimeSelect" style="font-size:12px;">Refresh every:</label>
                    <select id="refreshTimeSelect" style="margin:0 5px; font-size:12px;">
                        <option value="5">5s</option>
                        <option value="6">6s</option>
                        <option value="7">7s</option>
                        <option value="8">8s</option>
                        <option value="9">9s</option>
                        <option value="10">10s</option>
                    </select>
                    <button id="okBtn" style="margin-left:5px; font-size:12px;">OK</button>
                \`;

                document.body.appendChild(modal);
                console.log('[TrueBDC AutoRefresh] Modal created and added to iframe DOM');

                document.getElementById('refreshTimeSelect').value = refreshTime;
                document.getElementById('okBtn').onclick = function() {
                    console.log('[TrueBDC AutoRefresh] OK button clicked in iframe');
                    refreshTime = Number(document.getElementById('refreshTimeSelect').value);
                    localStorage.setItem('autoRefreshTime', refreshTime);
                    console.log('[TrueBDC AutoRefresh] New refresh time set in iframe:', refreshTime);
                    closeModal();
                    restart();
                };

                modal.addEventListener('click', e => e.stopPropagation());
                setTimeout(() => {
                    document.addEventListener('click', closeModal, { once: true });
                }, 0);
            }

            /**
             * Close the modal if open.
             */
            function closeModal() {
                console.log('[TrueBDC AutoRefresh] closeModal called in iframe');
                const modal = document.getElementById('refreshTimeModal');
                if (modal) {
                    modal.remove();
                    console.log('[TrueBDC AutoRefresh] Modal removed from iframe');
                }
                isPaused = false;
                updateDisplay();
            }

            /**
             * Update the timer display.
             */
            function updateDisplay() {
                createTimerDisplay();
            }

            /**
             * Start the auto-refresh timer.
             */
            function start() {
                console.log('[TrueBDC AutoRefresh] start() called in iframe');
                if (intervalId) {
                    clearInterval(intervalId);
                    console.log('[TrueBDC AutoRefresh] Cleared existing interval in iframe');
                }
                countdown = refreshTime;
                updateDisplay();
                intervalId = setInterval(() => {
                    if (!isPaused) {
                        countdown--;
                        updateDisplay();
                        console.log('[TrueBDC AutoRefresh] Countdown in iframe:', countdown);
                        if (countdown <= 0) {
                            console.log('[TrueBDC AutoRefresh] Countdown reached 0, refreshing iframe...');
                            document.getElementById('autoRefreshTimerDisplay').textContent = '🔄 Refreshing...';
                            setTimeout(() => window.location.reload(), 700);
                            clearInterval(intervalId);
                        }
                    }
                }, 1000);
                console.log('[TrueBDC AutoRefresh] Timer started in iframe with interval:', intervalId);
            }

            /**
             * Restart the timer.
             */
            function restart() {
                console.log('[TrueBDC AutoRefresh] restart() called in iframe');
                isPaused = false;
                start();
            }

            // Initialize in iframe
            console.log('[TrueBDC AutoRefresh] Initializing timer in iframe...');
            start();

            // Keyboard shortcut: Space to pause/resume
            document.addEventListener('keydown', e => {
                if (e.code === 'Space' && !document.getElementById('refreshTimeModal')) {
                    console.log('[TrueBDC AutoRefresh] Spacebar pressed in iframe, toggling pause');
                    isPaused = !isPaused;
                    updateDisplay();
                }
            });

            console.log('[TrueBDC AutoRefresh] Script initialization complete in iframe');
        `;
    }

    startScript() {
        console.log('[TrueBDC AutoRefresh] Starting the exact working script...');

        // EXACT COPY of the working Tampermonkey script
        (function() {
            'use strict';

            /** @type {number} */
            let intervalId = null;
            /** @type {boolean} */
            let isPaused = false;
            /** @type {number} */
            let countdown = 0;
            /** @type {number} */
            let refreshTime = Number(localStorage.getItem('autoRefreshTime')) || 5;

            console.log('[TrueBDC AutoRefresh] Script variables initialized:', { intervalId, isPaused, countdown, refreshTime });

            /**
             * Create or update the timer display in the corner.
             */
            function createTimerDisplay() {
                console.log('[TrueBDC AutoRefresh] Creating timer display...');
                let timer = document.getElementById('autoRefreshTimerDisplay');
                if (!timer) {
                    console.log('[TrueBDC AutoRefresh] Timer element not found, creating new one');
                    timer = document.createElement('div');
                    timer.id = 'autoRefreshTimerDisplay';
                    timer.style.position = 'fixed';
                    timer.style.bottom = '20px';
                    timer.style.left = '20px';
                    timer.style.background = '#fff';
                    timer.style.color = '#222';
                    timer.style.padding = '10px 18px';
                    timer.style.borderRadius = '8px';
                    timer.style.boxShadow = '0 2px 8px rgba(0,0,0,0.15)';
                    timer.style.fontFamily = 'sans-serif';
                    timer.style.fontSize = '16px';
                    timer.style.cursor = 'pointer';
                    timer.style.zIndex = 10000;
                    timer.title = 'Click to set refresh interval';
                    timer.addEventListener('click', showModal);
                    document.body.appendChild(timer);
                    console.log('[TrueBDC AutoRefresh] Timer element created and added to DOM');
                } else {
                    console.log('[TrueBDC AutoRefresh] Timer element found, updating content');
                }
                timer.textContent = isPaused ? `⏸️ Paused` : `🔄 Refresh in ${countdown}s`;
                timer.style.opacity = isPaused ? '0.5' : '1';
                console.log('[TrueBDC AutoRefresh] Timer display updated:', timer.textContent);
            }

            /**
             * Show a modal to set the refresh interval.
             */
            function showModal() {
                console.log('[TrueBDC AutoRefresh] showModal called');
                if (document.getElementById('refreshTimeModal')) {
                    console.log('[TrueBDC AutoRefresh] Modal already exists, returning');
                    return;
                }
                isPaused = true;
                updateDisplay();

                const timer = document.getElementById('autoRefreshTimerDisplay');
                const timerRect = timer.getBoundingClientRect();
                console.log('[TrueBDC AutoRefresh] Timer position:', timerRect);

                const modal = document.createElement('div');
                modal.id = 'refreshTimeModal';
                modal.style.position = 'fixed';
                modal.style.left = timerRect.left + 'px';
                modal.style.bottom = (window.innerHeight - timerRect.top) + 'px';
                modal.style.background = '#fff';
                modal.style.padding = '5px 7.5px'; // Modified: 50% reduced padding
                modal.style.borderRadius = '10px';
                modal.style.boxShadow = '0 4px 16px rgba(0,0,0,0.2)';
                modal.style.zIndex = 10001;
                modal.style.fontFamily = 'sans-serif';
                modal.style.fontSize = '12px'; // Modified: reduced font size

                modal.innerHTML = `
                    <label for="refreshTimeSelect" style="font-size:12px;">Refresh every:</label>
                    <select id="refreshTimeSelect" style="margin:0 5px; font-size:12px;">
                        <option value="5">5s</option>
                        <option value="6">6s</option>
                        <option value="7">7s</option>
                        <option value="8">8s</option>
                        <option value="9">9s</option>
                        <option value="10">10s</option>
                    </select>
                    <button id="okBtn" style="margin-left:5px; font-size:12px;">OK</button>
                `;

                document.body.appendChild(modal);
                console.log('[TrueBDC AutoRefresh] Modal created and added to DOM');

                document.getElementById('refreshTimeSelect').value = refreshTime;
                document.getElementById('okBtn').onclick = function() {
                    console.log('[TrueBDC AutoRefresh] OK button clicked');
                    refreshTime = Number(document.getElementById('refreshTimeSelect').value);
                    localStorage.setItem('autoRefreshTime', refreshTime);
                    console.log('[TrueBDC AutoRefresh] New refresh time set:', refreshTime);
                    closeModal();
                    restart();
                };

                modal.addEventListener('click', e => e.stopPropagation());
                setTimeout(() => {
                    document.addEventListener('click', closeModal, { once: true });
                }, 0);
            }

            /**
             * Close the modal if open.
             */
            function closeModal() {
                console.log('[TrueBDC AutoRefresh] closeModal called');
                const modal = document.getElementById('refreshTimeModal');
                if (modal) {
                    modal.remove();
                    console.log('[TrueBDC AutoRefresh] Modal removed');
                }
                isPaused = false;
                updateDisplay();
            }

            /**
             * Update the timer display.
             */
            function updateDisplay() {
                createTimerDisplay();
            }

            /**
             * Start the auto-refresh timer.
             */
            function start() {
                console.log('[TrueBDC AutoRefresh] start() called');
                if (intervalId) {
                    clearInterval(intervalId);
                    console.log('[TrueBDC AutoRefresh] Cleared existing interval');
                }
                countdown = refreshTime;
                updateDisplay();
                intervalId = setInterval(() => {
                    if (!isPaused) {
                        countdown--;
                        updateDisplay();
                        console.log('[TrueBDC AutoRefresh] Countdown:', countdown);
                        if (countdown <= 0) {
                            console.log('[TrueBDC AutoRefresh] Countdown reached 0, refreshing page...');
                            document.getElementById('autoRefreshTimerDisplay').textContent = '🔄 Refreshing...';
                            setTimeout(() => window.location.reload(), 700);
                            clearInterval(intervalId);
                        }
                    }
                }, 1000);
                console.log('[TrueBDC AutoRefresh] Timer started with interval:', intervalId);
            }

            /**
             * Restart the timer.
             */
            function restart() {
                console.log('[TrueBDC AutoRefresh] restart() called');
                isPaused = false;
                start();
            }

            // Initialize
            console.log('[TrueBDC AutoRefresh] Initializing timer...');
            start();

            // Keyboard shortcut: Space to pause/resume
            document.addEventListener('keydown', e => {
                if (e.code === 'Space' && !document.getElementById('refreshTimeModal')) {
                    console.log('[TrueBDC AutoRefresh] Spacebar pressed, toggling pause');
                    isPaused = !isPaused;
                    updateDisplay();
                }
            });

            console.log('[TrueBDC AutoRefresh] Script initialization complete');

        })();
    }

    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        console.log('[TrueBDC AutoRefresh] Settings updated:', newSettings);
    }

    onPageChange() {
        console.log('[TrueBDC AutoRefresh] Page change detected');
        if (this.isSupportedPage()) {
            if (!this.isActive) {
                this.init();
            }
        } else {
            if (this.isActive) {
                this.destroy();
            }
        }
    }

    destroy() {
        console.log('[TrueBDC AutoRefresh] Destroying script...');
        try {
            // Remove UI elements
            const timer = document.getElementById('autoRefreshTimerDisplay');
            if (timer && timer.parentNode) {
                timer.parentNode.removeChild(timer);
                console.log('[TrueBDC AutoRefresh] Timer element removed');
            }

            const modal = document.getElementById('refreshTimeModal');
            if (modal && modal.parentNode) {
                modal.parentNode.removeChild(modal);
                console.log('[TrueBDC AutoRefresh] Modal element removed');
            }

            this.isActive = false;
            console.log('[TrueBDC AutoRefresh] Script destroyed');
        } catch (error) {
            console.error('[TrueBDC AutoRefresh] Error destroying script:', error);
        }
    }

    static isAvailable() {
        const url = window.location.href;
        // For static method, use default pattern or check for eleadcrm.com
        return url.includes('eleadcrm.com');
    }

    getStatus() {
        return {
            isActive: this.isActive,
            isSupported: this.isSupportedPage(),
            hasTimer: !!document.getElementById('autoRefreshTimerDisplay'),
            currentUrl: window.location.href,
            targetPattern: '/elead_track/weblink/weblinkToday.aspx'
        };
    }

    // Manual test function for debugging
    forceStart() {
        console.log('[TrueBDC AutoRefresh] Force starting script regardless of URL...');
        this.startScript();
        this.isActive = true;
    }
}

// Make class globally available
window.AutoRefresh = AutoRefresh;

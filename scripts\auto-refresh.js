// TrueBDC CRM Automation Suite - Auto Refresh with Timer

class AutoRefresh {
    constructor(settings = {}) {
        this.settings = settings;
        this.isActive = false;
        this.isPaused = false;
        this.refreshInterval = null;
        this.countdownInterval = null;
        this.timerDisplay = null;
        this.progressBar = null;
        this.currentInterval = this.getRefreshInterval();
        this.timeRemaining = this.currentInterval;
        this.startTime = null;
        
        this.init();
    }

    init() {
        try {
            TrueBDCUtils.log('Initializing Auto Refresh Timer');

            // Check if we're on a supported page
            if (this.isSupportedPage()) {
                this.createTimerDisplay();
                this.createProgressBar();
                this.startTimer();
                this.isActive = true;
                
                TrueBDCUtils.log('Auto Refresh Timer activated', {
                    interval: this.currentInterval
                });
                TrueBDCUtils.logActivity('auto_refresh_activated', {
                    interval: this.currentInterval
                });
            } else {
                TrueBDCUtils.log('Auto Refresh Timer not activated - unsupported page');
            }
        } catch (error) {
            TrueBDCUtils.error('Failed to initialize Auto Refresh Timer', error);
        }
    }

    isSupportedPage() {
        const url = window.location.href;
        const supportedPatterns = [
            /eleadcrm\.com\/evo2\/fresh\/elead-v45\/elead_track/i,
            /vinsolutions\.com/i
        ];

        return supportedPatterns.some(pattern => pattern.test(url));
    }

    getRefreshInterval() {
        // Get interval from settings (in seconds), default to 7 seconds
        const intervalSeconds = this.settings.refreshInterval || 7;
        
        // Ensure interval is within bounds (5-10 seconds)
        return Math.max(5, Math.min(10, intervalSeconds)) * 1000; // Convert to milliseconds
    }

    createTimerDisplay() {
        // Remove existing timer if present
        const existing = document.getElementById('truebdc-refresh-timer');
        if (existing) {
            existing.remove();
        }

        this.timerDisplay = TrueBDCUtils.createElement('div', {
            id: 'truebdc-refresh-timer',
            class: 'truebdc-refresh-timer'
        }, {
            position: 'fixed',
            top: '10px',
            right: '10px',
            background: 'linear-gradient(135deg, #007bff, #0056b3)',
            color: 'white',
            padding: '8px 12px',
            borderRadius: '20px',
            fontSize: '12px',
            fontWeight: '500',
            zIndex: '999998',
            boxShadow: '0 2px 10px rgba(0, 123, 255, 0.3)',
            cursor: 'pointer',
            userSelect: 'none',
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
            transition: 'all 0.3s ease'
        });

        // Add click handler to toggle pause/resume
        this.timerDisplay.addEventListener('click', () => {
            this.togglePause();
        });

        // Add hover effects
        this.timerDisplay.addEventListener('mouseenter', () => {
            this.timerDisplay.style.transform = 'scale(1.05)';
        });

        this.timerDisplay.addEventListener('mouseleave', () => {
            this.timerDisplay.style.transform = 'scale(1)';
        });

        document.body.appendChild(this.timerDisplay);
        this.updateTimerDisplay();
    }

    createProgressBar() {
        // Remove existing progress bar if present
        const existing = document.getElementById('truebdc-progress-bar');
        if (existing) {
            existing.remove();
        }

        this.progressBar = TrueBDCUtils.createElement('div', {
            id: 'truebdc-progress-bar',
            class: 'truebdc-progress-bar'
        }, {
            position: 'fixed',
            top: '0',
            left: '0',
            height: '3px',
            background: 'linear-gradient(90deg, #007bff, #28a745)',
            zIndex: '999999',
            transition: 'width 1s linear',
            width: '100%'
        });

        document.body.appendChild(this.progressBar);
    }

    updateTimerDisplay() {
        if (!this.timerDisplay) return;

        const seconds = Math.ceil(this.timeRemaining / 1000);
        const statusIcon = this.isPaused ? '⏸️' : '🔄';
        const statusText = this.isPaused ? 'Paused' : `${seconds}s`;
        
        this.timerDisplay.innerHTML = `
            <span>${statusIcon}</span>
            <span>${statusText}</span>
            <span style="font-size: 10px; opacity: 0.8;">Click to ${this.isPaused ? 'resume' : 'pause'}</span>
        `;

        // Update styling based on state
        if (this.isPaused) {
            this.timerDisplay.style.background = 'linear-gradient(135deg, #ffc107, #e0a800)';
        } else {
            this.timerDisplay.style.background = 'linear-gradient(135deg, #007bff, #0056b3)';
        }
    }

    updateProgressBar() {
        if (!this.progressBar || this.isPaused) return;

        const elapsed = Date.now() - this.startTime;
        const progress = Math.max(0, Math.min(100, (elapsed / this.currentInterval) * 100));
        this.progressBar.style.width = `${100 - progress}%`;
    }

    startTimer() {
        this.stopTimer(); // Clear any existing timers
        
        this.startTime = Date.now();
        this.timeRemaining = this.currentInterval;
        
        // Update countdown every 100ms for smooth animation
        this.countdownInterval = setInterval(() => {
            if (!this.isPaused) {
                this.timeRemaining = this.currentInterval - (Date.now() - this.startTime);
                
                if (this.timeRemaining <= 0) {
                    this.performRefresh();
                } else {
                    this.updateTimerDisplay();
                    this.updateProgressBar();
                }
            }
        }, 100);

        TrueBDCUtils.log('Auto refresh timer started', {
            interval: this.currentInterval,
            intervalSeconds: this.currentInterval / 1000
        });
    }

    stopTimer() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
        
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }
    }

    togglePause() {
        this.isPaused = !this.isPaused;
        
        if (this.isPaused) {
            // Paused - save current time remaining
            this.timeRemaining = this.currentInterval - (Date.now() - this.startTime);
            TrueBDCUtils.log('Auto refresh paused', {
                timeRemaining: this.timeRemaining
            });
        } else {
            // Resumed - restart timer with remaining time
            this.startTime = Date.now() - (this.currentInterval - this.timeRemaining);
            TrueBDCUtils.log('Auto refresh resumed', {
                timeRemaining: this.timeRemaining
            });
        }
        
        this.updateTimerDisplay();
        
        TrueBDCUtils.logActivity('auto_refresh_toggled', {
            isPaused: this.isPaused,
            timeRemaining: this.timeRemaining
        });
    }

    performRefresh() {
        try {
            TrueBDCUtils.log('Performing auto refresh');
            
            // Show refresh notification
            this.showRefreshNotification();
            
            // Log activity
            TrueBDCUtils.logActivity('auto_refresh_triggered', {
                interval: this.currentInterval,
                timestamp: new Date().toISOString()
            });

            // Perform the refresh
            setTimeout(() => {
                location.reload(true);
            }, 500);

        } catch (error) {
            TrueBDCUtils.error('Error performing auto refresh', error);
            // Restart timer even if refresh fails
            this.startTimer();
        }
    }

    showRefreshNotification() {
        const notification = TrueBDCUtils.createElement('div', {
            id: 'truebdc-auto-refresh-notification'
        }, {
            position: 'fixed',
            top: '50px',
            right: '10px',
            background: 'linear-gradient(135deg, #28a745, #20c997)',
            color: 'white',
            padding: '12px 16px',
            borderRadius: '8px',
            fontSize: '14px',
            fontWeight: '500',
            zIndex: '999999',
            boxShadow: '0 4px 12px rgba(40, 167, 69, 0.3)',
            opacity: '0',
            transition: 'opacity 0.3s ease'
        });

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <div class="truebdc-spinner" style="width: 16px; height: 16px; border-width: 2px;"></div>
                <span>Auto refreshing page...</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Fade in
        setTimeout(() => {
            notification.style.opacity = '1';
        }, 10);
    }

    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        
        const newInterval = this.getRefreshInterval();
        if (newInterval !== this.currentInterval) {
            TrueBDCUtils.log('Auto refresh interval updated', {
                oldInterval: this.currentInterval,
                newInterval: newInterval
            });
            
            this.currentInterval = newInterval;
            
            // Restart timer with new interval
            if (this.isActive && !this.isPaused) {
                this.startTimer();
            }
            
            TrueBDCUtils.logActivity('auto_refresh_interval_updated', {
                newInterval: newInterval
            });
        }
    }

    onPageChange() {
        // Handle page changes in SPAs
        if (this.isSupportedPage()) {
            if (!this.isActive) {
                this.init();
            } else {
                // Restart timer on page change
                this.startTimer();
            }
        } else {
            if (this.isActive) {
                this.destroy();
            }
        }
    }

    destroy() {
        try {
            // Stop all timers
            this.stopTimer();

            // Remove UI elements
            if (this.timerDisplay && this.timerDisplay.parentNode) {
                this.timerDisplay.parentNode.removeChild(this.timerDisplay);
                this.timerDisplay = null;
            }

            if (this.progressBar && this.progressBar.parentNode) {
                this.progressBar.parentNode.removeChild(this.progressBar);
                this.progressBar = null;
            }

            this.isActive = false;
            this.isPaused = false;
            
            TrueBDCUtils.log('Auto Refresh Timer destroyed');
            TrueBDCUtils.logActivity('auto_refresh_destroyed');
        } catch (error) {
            TrueBDCUtils.error('Error destroying Auto Refresh Timer', error);
        }
    }

    // Static method to check if script should be available
    static isAvailable() {
        const url = window.location.href;
        return /eleadcrm\.com|vinsolutions\.com/i.test(url);
    }

    // Get current status
    getStatus() {
        return {
            isActive: this.isActive,
            isPaused: this.isPaused,
            isSupported: this.isSupportedPage(),
            currentInterval: this.currentInterval,
            timeRemaining: this.timeRemaining,
            hasTimerDisplay: !!this.timerDisplay,
            hasProgressBar: !!this.progressBar
        };
    }
}

// Make class globally available
window.AutoRefresh = AutoRefresh;

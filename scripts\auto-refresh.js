// TrueBDC CRM Automation Suite - Simple Auto Refresh with Timer and Modal

class AutoRefresh {
    constructor(settings = {}) {
        this.settings = settings;
        this.isActive = false;

        // Variables matching the original script exactly
        this.intervalId = null;
        this.isPaused = false;
        this.countdown = 0;
        this.refreshTime = Number(localStorage.getItem('autoRefreshTime')) || 5;

        this.init();
    }

    init() {
        try {
            TrueBDCUtils.log('Initializing Auto Refresh Timer');

            // Check if we're on a supported page
            if (this.isSupportedPage()) {
                this.setupKeyListener();
                this.start(); // Initialize exactly like the original
                this.isActive = true;

                TrueBDCUtils.log('Auto Refresh Timer activated', {
                    refreshTime: this.refreshTime
                });
                TrueBDCUtils.logActivity('auto_refresh_activated', {
                    refreshTime: this.refreshTime
                });
            } else {
                TrueBDCUtils.log('Auto Refresh Timer not activated - unsupported page');
            }
        } catch (error) {
            TrueBDCUtils.error('Failed to initialize Auto Refresh Timer', error);
        }
    }

    isSupportedPage() {
        const url = window.location.href;
        // EXACT URL pattern from the original working script
        return /eleadcrm\.com\/evo2\/fresh\/elead-v45\/elead_track\/weblink\/weblinkToday\.aspx/i.test(url);
    }

    setupKeyListener() {
        // Keyboard shortcut: Space to pause/resume - EXACTLY like original
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Space' && !document.getElementById('refreshTimeModal')) {
                this.isPaused = !this.isPaused;
                this.updateDisplay();
                TrueBDCUtils.log('Auto refresh toggled via spacebar', { isPaused: this.isPaused });
            }
        });
    }

    /**
     * Create or update the timer display in the corner - EXACTLY like original
     */
    createTimerDisplay() {
        let timer = document.getElementById('autoRefreshTimerDisplay');
        if (!timer) {
            timer = document.createElement('div');
            timer.id = 'autoRefreshTimerDisplay';
            timer.style.position = 'fixed';
            timer.style.bottom = '20px';
            timer.style.left = '20px';
            timer.style.background = '#fff';
            timer.style.color = '#222';
            timer.style.padding = '10px 18px';
            timer.style.borderRadius = '8px';
            timer.style.boxShadow = '0 2px 8px rgba(0,0,0,0.15)';
            timer.style.fontFamily = 'sans-serif';
            timer.style.fontSize = '16px';
            timer.style.cursor = 'pointer';
            timer.style.zIndex = 10000;
            timer.title = 'Click to set refresh interval';
            timer.addEventListener('click', () => this.showModal());
            document.body.appendChild(timer);
        }
        timer.textContent = this.isPaused ? `⏸️ Paused` : `🔄 Refresh in ${this.countdown}s`;
        timer.style.opacity = this.isPaused ? '0.5' : '1';
    }

    /**
     * Show a modal to set the refresh interval - EXACTLY like original with your modifications
     */
    showModal() {
        if (document.getElementById('refreshTimeModal')) return;
        this.isPaused = true;
        this.updateDisplay();

        const timer = document.getElementById('autoRefreshTimerDisplay');
        const timerRect = timer.getBoundingClientRect();

        const modal = document.createElement('div');
        modal.id = 'refreshTimeModal';
        modal.style.position = 'fixed';
        modal.style.left = timerRect.left + 'px';
        modal.style.bottom = (window.innerHeight - timerRect.top) + 'px';
        modal.style.background = '#fff';
        modal.style.padding = '5px 7.5px'; // YOUR MODIFICATION: Reduced by 50% from 10px 15px
        modal.style.borderRadius = '10px';
        modal.style.boxShadow = '0 4px 16px rgba(0,0,0,0.2)';
        modal.style.zIndex = '10001';
        modal.style.fontFamily = 'sans-serif';
        modal.style.fontSize = '12px'; // YOUR MODIFICATION: Changed from 15px to 12px

        modal.innerHTML = `
            <label for="refreshTimeSelect" style="font-size:12px;">Refresh every:</label>
            <select id="refreshTimeSelect" style="margin:0 5px; font-size:12px;">
                <option value="5">5s</option>
                <option value="6">6s</option>
                <option value="7">7s</option>
                <option value="8">8s</option>
                <option value="9">9s</option>
                <option value="10">10s</option>
            </select>
            <button id="okBtn" style="margin-left:5px; font-size:12px;">OK</button>
        `;

        document.body.appendChild(modal);

        document.getElementById('refreshTimeSelect').value = this.refreshTime;
        document.getElementById('okBtn').onclick = () => {
            this.refreshTime = Number(document.getElementById('refreshTimeSelect').value);
            localStorage.setItem('autoRefreshTime', this.refreshTime);
            this.closeModal();
            this.restart();
        };

        modal.addEventListener('click', e => e.stopPropagation());
        setTimeout(() => {
            document.addEventListener('click', () => this.closeModal(), { once: true });
        }, 0);
    }

    /**
     * Close the modal if open - EXACTLY like original
     */
    closeModal() {
        const modal = document.getElementById('refreshTimeModal');
        if (modal) modal.remove();
        this.isPaused = false;
        this.updateDisplay();
    }

    /**
     * Update the timer display - EXACTLY like original
     */
    updateDisplay() {
        this.createTimerDisplay();
    }

    /**
     * Start the auto-refresh timer - EXACTLY like original
     */
    start() {
        if (this.intervalId) clearInterval(this.intervalId);
        this.countdown = this.refreshTime;
        this.updateDisplay();
        this.intervalId = setInterval(() => {
            if (!this.isPaused) {
                this.countdown--;
                this.updateDisplay();
                if (this.countdown <= 0) {
                    document.getElementById('autoRefreshTimerDisplay').textContent = '🔄 Refreshing...';
                    setTimeout(() => window.location.reload(), 700);
                    clearInterval(this.intervalId);
                }
            }
        }, 1000);

        TrueBDCUtils.log('Auto refresh timer started', {
            refreshTime: this.refreshTime
        });
    }

    /**
     * Restart the timer - EXACTLY like original
     */
    restart() {
        this.isPaused = false;
        this.start();
    }

    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        TrueBDCUtils.log('Auto refresh settings updated', newSettings);
    }

    onPageChange() {
        // Handle page changes in SPAs
        if (this.isSupportedPage()) {
            if (!this.isActive) {
                this.init();
            } else {
                // Restart timer on page change
                this.restart();
            }
        } else {
            if (this.isActive) {
                this.destroy();
            }
        }
    }

    destroy() {
        try {
            // Stop timer - EXACTLY like original cleanup
            if (this.intervalId) {
                clearInterval(this.intervalId);
                this.intervalId = null;
            }

            // Remove UI elements
            const timer = document.getElementById('autoRefreshTimerDisplay');
            if (timer && timer.parentNode) {
                timer.parentNode.removeChild(timer);
            }

            // Remove modal if present
            this.closeModal();

            this.isActive = false;
            this.isPaused = false;

            TrueBDCUtils.log('Auto Refresh Timer destroyed');
            TrueBDCUtils.logActivity('auto_refresh_destroyed');
        } catch (error) {
            TrueBDCUtils.error('Error destroying Auto Refresh Timer', error);
        }
    }

    // Static method to check if script should be available
    static isAvailable() {
        const url = window.location.href;
        return /eleadcrm\.com\/evo2\/fresh\/elead-v45\/elead_track\/weblink\/weblinkToday\.aspx/i.test(url);
    }

    // Get current status
    getStatus() {
        return {
            isActive: this.isActive,
            isPaused: this.isPaused,
            isSupported: this.isSupportedPage(),
            refreshTime: this.refreshTime,
            countdown: this.countdown,
            hasTimer: !!document.getElementById('autoRefreshTimerDisplay'),
            shortcuts: {
                pauseResume: 'Spacebar (when modal is not open)'
            }
        };
    }
}

// Make class globally available
window.AutoRefresh = AutoRefresh;

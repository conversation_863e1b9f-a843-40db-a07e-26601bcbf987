// TrueBDC CRM Automation Suite - Dynamic Tab Title Changer

class DynamicTabTitle {
    constructor(settings = {}) {
        this.settings = settings;
        this.customTitle = settings.dealershipName || "Downtown Auto Center";
        this.observer = null;
        this.originalTitle = document.title;
        this.isActive = false;
        
        this.init();
    }

    init() {
        try {
            TrueBDCUtils.log('Initializing Dynamic Tab Title Changer', { 
                customTitle: this.customTitle,
                originalTitle: this.originalTitle 
            });

            // Check if we're on a supported page
            if (this.isSupportedPage()) {
                this.setCustomTitle();
                this.setupTitleObserver();
                this.isActive = true;
                
                TrueBDCUtils.log('Dynamic Tab Title Changer activated');
                TrueBDCUtils.logActivity('dynamic_tab_title_activated', {
                    customTitle: this.customTitle,
                    originalTitle: this.originalTitle
                });
            } else {
                TrueBDCUtils.log('Dynamic Tab Title Changer not activated - unsupported page');
            }
        } catch (error) {
            TrueBDCUtils.error('Failed to initialize Dynamic Tab Title Changer', error);
        }
    }

    isSupportedPage() {
        const url = window.location.href;
        const supportedPatterns = [
            /eleadcrm\.com\/evo2\/fresh\/elead-v45\/elead_track\/weblink\/weblinkToday\.aspx/i,
            /eleadcrm\.com\/evo2\/fresh\/elead-v45\/elead_track\/reports\/Desklog\/Desklog\.aspx/i,
            /eleadcrm\.com\/rt\/MessengerClient\/Home\/Index/i,
            /eleadcrm\.com\/evo2\/fresh\/elead-v45\/elead_track\/elead_mail\/Mailbox\.aspx\?Type=M/i,
            /vinsolutions\.com/i // Support VinSolutions as well
        ];

        return supportedPatterns.some(pattern => pattern.test(url));
    }

    setCustomTitle() {
        if (document.title !== this.customTitle) {
            document.title = this.customTitle;
            TrueBDCUtils.log('Tab title changed', { 
                from: this.originalTitle, 
                to: this.customTitle 
            });
        }
    }

    setupTitleObserver() {
        // Observe changes to the title element
        const titleElement = document.querySelector('title');
        if (titleElement) {
            this.observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList' && document.title !== this.customTitle) {
                        TrueBDCUtils.log('Title change detected, restoring custom title', {
                            detectedTitle: document.title,
                            customTitle: this.customTitle
                        });
                        this.setCustomTitle();
                    }
                });
            });

            this.observer.observe(titleElement, { 
                childList: true, 
                characterData: true,
                subtree: true 
            });
        }

        // Also observe the document head for title changes
        const headElement = document.head;
        if (headElement) {
            const headObserver = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach((node) => {
                            if (node.nodeName === 'TITLE') {
                                // New title element added, observe it
                                this.observer.observe(node, { 
                                    childList: true, 
                                    characterData: true,
                                    subtree: true 
                                });
                                this.setCustomTitle();
                            }
                        });
                    }
                });
            });

            headObserver.observe(headElement, { childList: true });
        }

        // Periodic check as fallback
        this.titleCheckInterval = setInterval(() => {
            if (document.title !== this.customTitle) {
                this.setCustomTitle();
            }
        }, 2000);
    }

    updateSettings(newSettings) {
        const oldTitle = this.customTitle;
        this.settings = { ...this.settings, ...newSettings };
        this.customTitle = newSettings.dealershipName || this.customTitle;

        if (oldTitle !== this.customTitle && this.isActive) {
            TrueBDCUtils.log('Updating custom title', { 
                from: oldTitle, 
                to: this.customTitle 
            });
            this.setCustomTitle();
            
            TrueBDCUtils.logActivity('dynamic_tab_title_updated', {
                oldTitle: oldTitle,
                newTitle: this.customTitle
            });
        }
    }

    onPageChange() {
        // Handle page changes in SPAs
        if (this.isSupportedPage()) {
            if (!this.isActive) {
                this.init();
            } else {
                this.setCustomTitle();
            }
        } else {
            if (this.isActive) {
                this.restoreOriginalTitle();
                this.isActive = false;
            }
        }
    }

    restoreOriginalTitle() {
        if (this.originalTitle && document.title === this.customTitle) {
            document.title = this.originalTitle;
            TrueBDCUtils.log('Original title restored', { 
                originalTitle: this.originalTitle 
            });
        }
    }

    destroy() {
        try {
            // Stop observing
            if (this.observer) {
                this.observer.disconnect();
                this.observer = null;
            }

            // Clear interval
            if (this.titleCheckInterval) {
                clearInterval(this.titleCheckInterval);
                this.titleCheckInterval = null;
            }

            // Restore original title
            this.restoreOriginalTitle();

            this.isActive = false;
            
            TrueBDCUtils.log('Dynamic Tab Title Changer destroyed');
            TrueBDCUtils.logActivity('dynamic_tab_title_destroyed');
        } catch (error) {
            TrueBDCUtils.error('Error destroying Dynamic Tab Title Changer', error);
        }
    }

    // Static method to check if script should be available
    static isAvailable() {
        const url = window.location.href;
        return /eleadcrm\.com|vinsolutions\.com/i.test(url);
    }

    // Get current status
    getStatus() {
        return {
            isActive: this.isActive,
            customTitle: this.customTitle,
            originalTitle: this.originalTitle,
            currentTitle: document.title,
            isSupported: this.isSupportedPage()
        };
    }
}

// Make class globally available
window.DynamicTabTitle = DynamicTabTitle;
